package com.gl.service.message.controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.gl.framework.web.response.Result;
import com.gl.service.message.service.BaseServiceMessageService;
import com.gl.service.message.vo.BaseServiceMessageVo;
import com.gl.service.message.vo.dto.BaseServiceMessageDto;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.test.context.junit.jupiter.SpringExtension;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.context.annotation.Import;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.security.test.context.support.WithMockUser;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.boot.test.mock.mockito.MockBean;
import com.gl.framework.security.service.PermissionService;

import java.util.*;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;
import static org.springframework.security.test.web.servlet.request.SecurityMockMvcRequestPostProcessors.csrf;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultHandlers.print;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

/**
 * BaseServiceMessageController单元测试类
 * 测试客服留言控制器的所有HTTP接口，包括权限验证和请求响应处理
 *
 * @author: Test Author
 * @date: 2025-07-12
 * @version: 1.0
 */
@ExtendWith(SpringExtension.class)
@WebMvcTest(controllers = BaseServiceMessageController.class, excludeAutoConfiguration = {})
@ActiveProfiles("test")
@DisplayName("BaseServiceMessageController单元测试")
class BaseServiceMessageControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private BaseServiceMessageService baseServiceMessageService;

    @MockBean
    private PermissionService permissionService;

    @Autowired
    private ObjectMapper objectMapper;

    private BaseServiceMessageDto testDto;
    private Result successResult;
    private Result failResult;
    private List<BaseServiceMessageVo> testMessageList;

    @BeforeEach
    @DisplayName("测试数据初始化")
    void setUp() {
        // 初始化测试DTO
        testDto = new BaseServiceMessageDto();
        testDto.setSearchCondition("测试留言");
        testDto.setIds(Arrays.asList(1L, 2L, 3L));

        // 初始化测试留言列表
        BaseServiceMessageVo testMessage = new BaseServiceMessageVo();
        testMessage.setId(1L);
        testMessage.setUserId(100L);
        testMessage.setContent("这是一条测试留言");
        testMessage.setMessageTime(new Date());
        testMessage.setNickname("测试用户");
        testMessage.setPhone("13800138000");
        testMessage.setAvatar("http://example.com/avatar.jpg");
        testMessageList = Arrays.asList(testMessage);

        // 初始化成功结果
        successResult = Result.success();
        Map<String, Object> data = new HashMap<>();
        data.put("total", 1L);
        data.put("result", testMessageList);
        successResult.setData(data);

        // 初始化失败结果
        failResult = Result.fail("操作失败");

        // 配置权限服务模拟行为
        when(permissionService.hasPermi("message:message:list")).thenReturn(true);
        when(permissionService.hasPermi("message:message:delete")).thenReturn(true);
        when(permissionService.hasPermi("other:permission")).thenReturn(false);
    }

    // ==================== list方法测试 ====================

    @Test
    @DisplayName("测试GET /message - 有权限用户正常查询")
    @WithMockUser(authorities = "message:message:list")
    void testList_WithValidPermission_ShouldReturnSuccessResult() throws Exception {
        // Given
        when(baseServiceMessageService.list(any(BaseServiceMessageDto.class)))
                .thenReturn(successResult);

        // When & Then
        mockMvc.perform(get("/message")
                        .param("searchCondition", "测试留言")
                        .param("pageNumber", "0")
                        .param("pageSize", "10")
                        .contentType(MediaType.APPLICATION_JSON))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(10000))
                .andExpect(jsonPath("$.message").value("success"))
                .andExpect(jsonPath("$.data.total").value(1))
                .andExpect(jsonPath("$.data.result").isArray())
                .andExpect(jsonPath("$.data.result[0].id").value(1))
                .andExpect(jsonPath("$.data.result[0].content").value("这是一条测试留言"));

        verify(baseServiceMessageService, times(1)).list(any(BaseServiceMessageDto.class));
    }

    @Test
    @DisplayName("测试GET /message - 无权限用户访问")
    @WithMockUser(authorities = "other:permission")
    void testList_WithoutPermission_ShouldReturnForbidden() throws Exception {
        // When & Then
        mockMvc.perform(get("/message")
                        .param("searchCondition", "测试留言")
                        .contentType(MediaType.APPLICATION_JSON))
                .andDo(print())
                .andExpect(status().isForbidden());

        verify(baseServiceMessageService, never()).list(any(BaseServiceMessageDto.class));
    }

    @Test
    @DisplayName("测试GET /message - 未认证用户访问")
    void testList_WithoutAuthentication_ShouldReturnUnauthorized() throws Exception {
        // When & Then
        mockMvc.perform(get("/message")
                        .param("searchCondition", "测试留言")
                        .contentType(MediaType.APPLICATION_JSON))
                .andDo(print())
                .andExpect(status().isUnauthorized());

        verify(baseServiceMessageService, never()).list(any(BaseServiceMessageDto.class));
    }

    @Test
    @DisplayName("测试GET /message - 无搜索条件查询")
    @WithMockUser(authorities = "message:message:list")
    void testList_WithoutSearchCondition_ShouldReturnSuccessResult() throws Exception {
        // Given
        when(baseServiceMessageService.list(any(BaseServiceMessageDto.class)))
                .thenReturn(successResult);

        // When & Then
        mockMvc.perform(get("/message")
                        .param("pageNumber", "0")
                        .param("pageSize", "10")
                        .contentType(MediaType.APPLICATION_JSON))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(10000))
                .andExpect(jsonPath("$.message").value("success"));

        verify(baseServiceMessageService, times(1)).list(any(BaseServiceMessageDto.class));
    }

    @Test
    @DisplayName("测试GET /message - 服务层返回失败结果")
    @WithMockUser(authorities = "message:message:list")
    void testList_WithServiceFailure_ShouldReturnFailResult() throws Exception {
        // Given
        when(baseServiceMessageService.list(any(BaseServiceMessageDto.class)))
                .thenReturn(failResult);

        // When & Then
        mockMvc.perform(get("/message")
                        .param("searchCondition", "测试留言")
                        .contentType(MediaType.APPLICATION_JSON))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(10001))
                .andExpect(jsonPath("$.message").value("操作失败"));

        verify(baseServiceMessageService, times(1)).list(any(BaseServiceMessageDto.class));
    }

    @Test
    @DisplayName("测试GET /message - 服务层抛出异常")
    @WithMockUser(authorities = "message:message:list")
    void testList_WithServiceException_ShouldReturnError() throws Exception {
        // Given
        when(baseServiceMessageService.list(any(BaseServiceMessageDto.class)))
                .thenThrow(new RuntimeException("数据库连接异常"));

        // When & Then
        mockMvc.perform(get("/message")
                        .param("searchCondition", "测试留言")
                        .contentType(MediaType.APPLICATION_JSON))
                .andDo(print())
                .andExpect(status().isInternalServerError());

        verify(baseServiceMessageService, times(1)).list(any(BaseServiceMessageDto.class));
    }

    // ==================== delete方法测试 ====================

    @Test
    @DisplayName("测试DELETE /message - 有权限用户正常删除")
    @WithMockUser(authorities = "message:message:delete")
    void testDelete_WithValidPermission_ShouldReturnSuccessResult() throws Exception {
        // Given
        Result deleteSuccessResult = Result.success();
        when(baseServiceMessageService.delete(any(BaseServiceMessageDto.class)))
                .thenReturn(deleteSuccessResult);

        // When & Then
        mockMvc.perform(delete("/message")
                        .with(csrf())
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(testDto)))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(10000))
                .andExpect(jsonPath("$.message").value("success"));

        verify(baseServiceMessageService, times(1)).delete(any(BaseServiceMessageDto.class));
    }

    @Test
    @DisplayName("测试DELETE /message - 无权限用户访问")
    @WithMockUser(authorities = "other:permission")
    void testDelete_WithoutPermission_ShouldReturnForbidden() throws Exception {
        // When & Then
        mockMvc.perform(delete("/message")
                        .with(csrf())
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(testDto)))
                .andDo(print())
                .andExpect(status().isForbidden());

        verify(baseServiceMessageService, never()).delete(any(BaseServiceMessageDto.class));
    }

    @Test
    @DisplayName("测试DELETE /message - 未认证用户访问")
    void testDelete_WithoutAuthentication_ShouldReturnUnauthorized() throws Exception {
        // When & Then
        mockMvc.perform(delete("/message")
                        .with(csrf())
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(testDto)))
                .andDo(print())
                .andExpect(status().isUnauthorized());

        verify(baseServiceMessageService, never()).delete(any(BaseServiceMessageDto.class));
    }

    @Test
    @DisplayName("测试DELETE /message - 请求体为空")
    @WithMockUser(authorities = "message:message:delete")
    void testDelete_WithEmptyRequestBody_ShouldReturnBadRequest() throws Exception {
        // When & Then
        mockMvc.perform(delete("/message")
                        .with(csrf())
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(""))
                .andDo(print())
                .andExpect(status().isBadRequest());

        verify(baseServiceMessageService, never()).delete(any(BaseServiceMessageDto.class));
    }

    @Test
    @DisplayName("测试DELETE /message - 无效JSON格式")
    @WithMockUser(authorities = "message:message:delete")
    void testDelete_WithInvalidJson_ShouldReturnBadRequest() throws Exception {
        // When & Then
        mockMvc.perform(delete("/message")
                        .with(csrf())
                        .contentType(MediaType.APPLICATION_JSON)
                        .content("{invalid json}"))
                .andDo(print())
                .andExpect(status().isBadRequest());

        verify(baseServiceMessageService, never()).delete(any(BaseServiceMessageDto.class));
    }

    @Test
    @DisplayName("测试DELETE /message - 服务层返回失败结果")
    @WithMockUser(authorities = "message:message:delete")
    void testDelete_WithServiceFailure_ShouldReturnFailResult() throws Exception {
        // Given
        Result deleteFailResult = Result.fail("留言id不能为空");
        when(baseServiceMessageService.delete(any(BaseServiceMessageDto.class)))
                .thenReturn(deleteFailResult);

        // When & Then
        mockMvc.perform(delete("/message")
                        .with(csrf())
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(testDto)))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(10001))
                .andExpect(jsonPath("$.message").value("留言id不能为空"));

        verify(baseServiceMessageService, times(1)).delete(any(BaseServiceMessageDto.class));
    }

    @Test
    @DisplayName("测试DELETE /message - 服务层抛出异常")
    @WithMockUser(authorities = "message:message:delete")
    void testDelete_WithServiceException_ShouldReturnError() throws Exception {
        // Given
        when(baseServiceMessageService.delete(any(BaseServiceMessageDto.class)))
                .thenThrow(new RuntimeException("数据库删除异常"));

        // When & Then
        mockMvc.perform(delete("/message")
                        .with(csrf())
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(testDto)))
                .andDo(print())
                .andExpect(status().isInternalServerError());

        verify(baseServiceMessageService, times(1)).delete(any(BaseServiceMessageDto.class));
    }

    @Test
    @DisplayName("测试DELETE /message - 删除单个留言")
    @WithMockUser(authorities = "message:message:delete")
    void testDelete_WithSingleId_ShouldReturnSuccessResult() throws Exception {
        // Given
        BaseServiceMessageDto singleIdDto = new BaseServiceMessageDto();
        singleIdDto.setIds(Arrays.asList(1L));

        Result deleteSuccessResult = Result.success();
        when(baseServiceMessageService.delete(any(BaseServiceMessageDto.class)))
                .thenReturn(deleteSuccessResult);

        // When & Then
        mockMvc.perform(delete("/message")
                        .with(csrf())
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(singleIdDto)))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(10000))
                .andExpect(jsonPath("$.message").value("success"));

        verify(baseServiceMessageService, times(1)).delete(any(BaseServiceMessageDto.class));
    }

    @Test
    @DisplayName("测试DELETE /message - 缺少CSRF令牌")
    @WithMockUser(authorities = "message:message:delete")
    void testDelete_WithoutCsrfToken_ShouldReturnForbidden() throws Exception {
        // When & Then
        mockMvc.perform(delete("/message")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(testDto)))
                .andDo(print())
                .andExpect(status().isForbidden());

        verify(baseServiceMessageService, never()).delete(any(BaseServiceMessageDto.class));
    }
}
